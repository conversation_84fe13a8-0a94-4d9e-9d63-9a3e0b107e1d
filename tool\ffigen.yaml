name: IsarCoreBindings
output: "lib/src/native/bindings.dart"
headers:
  entry-points:
    - "isar-dart.h"
  include-directives:
    - '**isar-dart.h'

structs:
  dependency-only: opaque
  include:
    - CObject
    - CObjectSet
    - CObjectCollectionSet
    - CLink
    - CLinkSet
    - CObjectLinkSet
  rename:
    '^(?!C)(.*)': 'C$1'
  
unions:
  dependency-only: opaque
  include:
    - 'isar*'

preamble: |
  // ignore_for_file: camel_case_types, non_constant_identifier_names
