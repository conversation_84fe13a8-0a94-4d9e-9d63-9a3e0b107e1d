[package]
name = "isar-core"
version = "0.0.0"
authors = ["<PERSON> <simon<PERSON><PERSON>@gmail.com>"]
edition = "2021"

[dependencies]
itertools = "0.10.3"
enum_dispatch = "0.3.8"
ffi = { package = "mdbx-sys", path = "../mdbx_sys" }
libc = "0.2"
xxhash-rust = { version = "0.8.5", features = ["xxh3"] }
serde =  { version = "1.0", features = ["derive"] }
serde_json = "1.0"
once_cell = "1.10.0"
crossbeam-channel = "0.5.4"
byteorder = "1"
paste = "1.0"
intmap = "2.0.0"
snafu = "0.7.0"

[target.'cfg(target_os = "windows")'.dependencies]
widestring = "1.0"

[dev-dependencies]
rand = "0.8.5"
cfg-if = "1"
float_next_after = "0.1"

[dev-dependencies.serde_json]
version = "*"
features = ["float_roundtrip"]
