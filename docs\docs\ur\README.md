---
home: true
title: ہوم
heroImage: /isar.svg
actions:
  - text:  آئیے شروع کریں۔
    link: /tutorials/quickstart.html
    type: primary
features:
  - title:  کے لیے بنایا گیاہے💙 Flutter
    details:  کم سے کم سیٹ اپ، استعمال میں آسان، کوئی ترتیب نہیں، کوئی بوائلر پلیٹ نہیں۔ شروع کرنے کے لیے بس کوڈ کی چند لائنیں شامل کریں۔
  - title: 🚀 انتہائی قابل توسیع
    details:  ایک ہی نو ایس کیو ایل ڈیٹا بیس میں سیکڑوں ہزاروں ریکارڈز کو ذخیرہ کریں اور ان سے موثر اور متضاد طور پر استفسار کریں۔
  - title: 🍭 خصوصیت سے بھرپور
    details:  آپ کے ڈیٹا کو منظم کرنے میں آپ کی مدد کرنے کے لیے ای زار کے پاس خصوصیات کا ایک بھرپور مجموعہ ہے۔ کمپوزٹ اور ملٹی انٹری انڈیکس، استفسار میں ترمیم کرنے والے، جےسن سپورٹ، اور بہت کچھ۔
  - title: 🔎 مکمل متن کی تلاش
    details:  ای زار کے پاس بنی بنائں مکمل متن تلاشی ہے۔ ملٹی انٹری انڈیکس بنائیں اور آسانی سے ریکارڈ تلاش کریں۔
  - title:  🧪ایسڈ سیمنٹکس
    details:  ای زار تیزاب کے مطابق ہے اور لین دین کو خود بخود ہینڈل کرتا ہے۔ اگر کوئی خرابی پیش آتی ہے تو یہ تبدیلیوں کو واپس لے لیتا ہے۔
  - title: 💃 جامد ٹائپنگ
    details: ای زار کے سوالات کو جامد طور پر ٹائپ کیا جاتا ہے اور مرتب وقت کی جانچ پڑتال کی جاتی ہے۔ رن ٹائم غلطیوں کے بارے میں فکر کرنے کی ضرورت نہیں ہے۔ 
  - title: 📱 ملٹی پلیٹ فارم
    details: iOS, Android, Desktop, اور مکمل WEB SUPPORT!
  - title: ⏱ غیر مطابقت پذیر
    details:  متوازی استفسار کے آپریشنز اور ملٹی آئسولیٹ سپورٹ آؤٹ آف دی باکس
  - title: 🦄 اوپن سورس
    details: سب کچھ اوپن سورس اور ہمیشہ کے لیے مفت ہے!

footer: Apache Licensed | Copyright © 2022 Simon Leier
---
