name: pub_app
description: A new Flutter project.
publish_to: "none"
version: 1.0.0+1

environment:
  sdk: ">=2.17.0 <4.0.0"
  flutter: ">=1.17.0"

isar_version: &isar_version 3.0.2 # define the version to be used

dependencies:
  auto_size_text: ^3.0.0
  clickup_fading_scroll:
    git:
      url: https://github.com/clickup/clickup_fading_scroll.git
  dio: ^4.0.0
  flutter:
    sdk: flutter
  flutter_riverpod: ^2.0.0
  google_fonts: ^3.0.1
  isar_community: *isar_version
  isar_community_flutter_libs: *isar_version
  json_serializable: ^6.0.0
  json_annotation: ^4.6.0
  markdown: ^6.0.0
  path_provider: ^2.1.1
  pub_semver: ^2.1.1
  pubspec: ^2.3.0
  pubspec_parse: ^1.2.1
  riverpod: ^2.0.0
  shimmer: ^2.0.0
  tar: ^0.5.6
  timeago: ^3.3.0
  url_launcher: ^6.1.5
  go_router: ^5.0.0
  flutter_svg: ^1.1.5
  flutter_html: ^3.0.0-alpha.6
  flutter_html_svg: ^3.0.0-alpha.4

dev_dependencies:
  build_runner: ^2.0.0
  flutter_test:
    sdk: flutter
  isar_community_generator: *isar_version
  flutter_lints: ^2.0.1

flutter:
  uses-material-design: true
  assets:
    - assets/ff_banner.png
    - assets/pub_logo.svg
    - assets/pub_logo_dark.svg
    - assets/search_bg.svg

dependency_overrides:
  isar_community:
    path: ../../packages/isar_community
  isar_community_flutter_libs:
    path: ../../packages/isar_community_flutter_libs

  # dev
  isar_community_generator:
    path: ../../packages/isar_community_generator
