---
title: よくある質問
---

# よくある質問

IsarとFlutterのデータベースについてよくある質問を無作為に集めました。

### 何でデータベースが必要なの？

> 私はバックエンドDBにデータを保存しているけど, 何でIsarが必要なの？

地下鉄や飛行機に乗っているとき、あるいはWiFiがなく携帯の電波が非常に悪いおばあちゃんの家に行ったときなど、今日においてもデータ通信ができないことはよくあることです。接続が悪いとアプリ自体が使えないなんてことがあってはなりません。

### Isar vs Hive

答えは簡単です: Isar は [Hiveの代替としてスタート](https://github.com/hivedb/hive/issues/246) し、現在はHiveよりもIsarを使うことを推奨する状態になっています。

### WHERE節って！？

> 何でwhere節にどのインデックスを使うのか選択しなきゃいけないの？

これには複数の理由があります。ほとんどのデータベースは、与えられたクエリに対して最適なインデックスを選択するためにヒューリスティックを使用しています。データベースは追加の利用状況データを収集する必要があり、それがオーバーヘッドに繋がります。 加えて、それでも間違ったインデックスを選択する可能性があります。 更にはクエリの作成が遅くなるという点も懸念されます。

開発者であるあなた以上に、あなたのデータを知っている人はいません。ですから、あなた自身が最適なインデックスを選択し、例えば、クエリにインデックスを使うかソートにインデックスを使うかなどを決定することができるのです。

### インデックスやwhere節を必ず使わないといけないの？

いえ、必ずしもそんなことはありません。多くの場合、filterしか使用しなかったとしてもIsarは高速で処理されます。

### Isarって速いの？

Isarは、モバイル向けデータベースの中では最速クラスなので、ほとんどのケースでは十分な速度が出るはずです。もし、パフォーマンスで問題が発生した場合は、何か間違った操作をしている可能性があります。

### Isarはアプリのサイズを増加させますか？

そうですね、少しは。Isarは、アプリのダウンロードサイズを約1〜1.5MB増加させます。Isar Webは、数KBの追加にとどまります。

### ドキュメントの内容が間違ってるよ / タイポ見つけた

すみません、失礼致しました。 [issueを開く](https://github.com/isar-community/isar/issues/new/choose)か、もしくは、プルリクエストをして修正して頂けると助かります💪
