@TestOn('vm')

import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:isar_community/isar.dart';
import 'package:isar_community/src/native/isar_core.dart';
import 'package:isar_community/src/native/isar_reader_impl.dart';
import 'package:isar_community/src/native/isar_writer_impl.dart';
import 'package:test/test.dart';

void main() {
  group('Performance Tests', () {
    test('IsarWriter should handle bulk integer writes efficiently', () {
      final buffer = Uint8List(400000); // 100k integers
      final writer = IsarWriterImpl(buffer, 400000);
      
      final stopwatch = Stopwatch()..start();
      
      // Write 100,000 integers
      for (int i = 0; i < 100000; i++) {
        writer.writeInt(i * 4, i);
      }
      
      stopwatch.stop();
      
      print('Bulk integer write (100k): ${stopwatch.elapsedMilliseconds}ms');
      
      // Verify performance is reasonable (should be very fast)
      expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // Less than 1 second
      
      // Verify data integrity
      final reader = IsarReaderImpl(buffer);
      expect(reader.readInt(0), equals(0));
      expect(reader.readInt(99999 * 4), equals(99999));
    });

    test('IsarReader should handle bulk integer reads efficiently', () {
      final buffer = Uint8List(400000);
      final writer = IsarWriterImpl(buffer, 400000);
      
      // Prepare test data
      for (int i = 0; i < 100000; i++) {
        writer.writeInt(i * 4, i);
      }
      
      final reader = IsarReaderImpl(buffer);
      final stopwatch = Stopwatch()..start();
      
      // Read 100,000 integers
      var sum = 0;
      for (int i = 0; i < 100000; i++) {
        sum += reader.readInt(i * 4);
      }
      
      stopwatch.stop();
      
      print('Bulk integer read (100k): ${stopwatch.elapsedMilliseconds}ms');
      
      // Verify performance and correctness
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      expect(sum, equals(4999950000)); // Sum of 0 to 99999
    });

    test('IsarWriter should handle string operations efficiently', () {
      final buffer = Uint8List(1000000);
      final writer = IsarWriterImpl(buffer, 1000000);
      
      final testStrings = List.generate(1000, (i) => 'Test string $i with some content');
      final stopwatch = Stopwatch()..start();
      
      var offset = 0;
      for (final str in testStrings) {
        final bytes = utf8.encode(str);
        writer.writeByteList(offset, Uint8List.fromList(bytes));
        offset += 1000; // Reserve space between strings
      }
      
      stopwatch.stop();
      
      print('String write (1k strings): ${stopwatch.elapsedMilliseconds}ms');
      
      expect(stopwatch.elapsedMilliseconds, lessThan(500));
      
      // Verify a few strings
      final reader = IsarReaderImpl(buffer);
      final firstBytes = reader.readByteList(0);
      expect(utf8.decode(firstBytes!), equals(testStrings[0]));
    });

    test('IsarReader should handle string operations efficiently', () {
      final buffer = Uint8List(1000000);
      final writer = IsarWriterImpl(buffer, 1000000);
      
      final testStrings = List.generate(1000, (i) => 'Test string $i');
      
      // Prepare data
      var offset = 0;
      for (final str in testStrings) {
        final bytes = utf8.encode(str);
        writer.writeByteList(offset, Uint8List.fromList(bytes));
        offset += 1000;
      }
      
      final reader = IsarReaderImpl(buffer);
      final stopwatch = Stopwatch()..start();
      
      // Read all strings
      offset = 0;
      final readStrings = <String>[];
      for (int i = 0; i < 1000; i++) {
        final bytes = reader.readByteList(offset);
        if (bytes != null) {
          readStrings.add(utf8.decode(bytes));
        }
        offset += 1000;
      }
      
      stopwatch.stop();
      
      print('String read (1k strings): ${stopwatch.elapsedMilliseconds}ms');
      
      expect(stopwatch.elapsedMilliseconds, lessThan(500));
      expect(readStrings.length, equals(1000));
      expect(readStrings[0], equals(testStrings[0]));
    });

    test('IsarWriter should handle list operations efficiently', () {
      final buffer = Uint8List(2000000);
      final writer = IsarWriterImpl(buffer, 2000000);
      
      final testLists = List.generate(100, (i) => 
        List.generate(1000, (j) => i * 1000 + j)
      );
      
      final stopwatch = Stopwatch()..start();
      
      var offset = 0;
      for (final list in testLists) {
        writer.writeIntList(offset, list);
        offset += 20000; // Reserve space for each list
      }
      
      stopwatch.stop();
      
      print('List write (100 lists of 1k ints): ${stopwatch.elapsedMilliseconds}ms');
      
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      
      // Verify first list
      final reader = IsarReaderImpl(buffer);
      final firstList = reader.readIntList(0);
      expect(firstList, equals(testLists[0]));
    });

    test('IsarReader should handle list operations efficiently', () {
      final buffer = Uint8List(2000000);
      final writer = IsarWriterImpl(buffer, 2000000);
      
      final testLists = List.generate(100, (i) => 
        List.generate(1000, (j) => i * 1000 + j)
      );
      
      // Prepare data
      var offset = 0;
      for (final list in testLists) {
        writer.writeIntList(offset, list);
        offset += 20000;
      }
      
      final reader = IsarReaderImpl(buffer);
      final stopwatch = Stopwatch()..start();
      
      // Read all lists
      offset = 0;
      final readLists = <List<int>>[];
      for (int i = 0; i < 100; i++) {
        final list = reader.readIntList(offset);
        if (list != null) {
          readLists.add(list);
        }
        offset += 20000;
      }
      
      stopwatch.stop();
      
      print('List read (100 lists of 1k ints): ${stopwatch.elapsedMilliseconds}ms');
      
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      expect(readLists.length, equals(100));
      expect(readLists[0], equals(testLists[0]));
    });

    test('IsarWriter should handle mixed data types efficiently', () {
      final buffer = Uint8List(1000000);
      final writer = IsarWriterImpl(buffer, 1000000);
      
      final stopwatch = Stopwatch()..start();
      
      var offset = 0;
      for (int i = 0; i < 10000; i++) {
        // Write mixed data for each record
        writer.writeInt(offset, i);
        offset += 4;
        
        writer.writeLong(offset, i * 1000);
        offset += 8;
        
        writer.writeFloat(offset, i * 3.14);
        offset += 4;
        
        writer.writeBool(offset, i % 2 == 0);
        offset += 1;
        
        final str = 'Record $i';
        final bytes = utf8.encode(str);
        writer.writeByteList(offset, Uint8List.fromList(bytes));
        offset += 50; // Fixed space for string
      }
      
      stopwatch.stop();
      
      print('Mixed data write (10k records): ${stopwatch.elapsedMilliseconds}ms');
      
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      
      // Verify first record
      final reader = IsarReaderImpl(buffer);
      expect(reader.readInt(0), equals(0));
      expect(reader.readLong(4), equals(0));
      expect(reader.readFloat(12), closeTo(0.0, 0.001));
      expect(reader.readBool(16), isTrue);
    });

    test('IsarReader should handle random access efficiently', () {
      final buffer = Uint8List(400000);
      final writer = IsarWriterImpl(buffer, 400000);
      
      // Prepare data
      for (int i = 0; i < 100000; i++) {
        writer.writeInt(i * 4, i * i); // Square values
      }
      
      final reader = IsarReaderImpl(buffer);
      
      // Generate random access pattern
      final random = List.generate(10000, (i) => (i * 7919) % 100000);
      
      final stopwatch = Stopwatch()..start();
      
      var sum = 0;
      for (final index in random) {
        sum += reader.readInt(index * 4);
      }
      
      stopwatch.stop();
      
      print('Random access read (10k accesses): ${stopwatch.elapsedMilliseconds}ms');
      
      expect(stopwatch.elapsedMilliseconds, lessThan(500));
      expect(sum, greaterThan(0)); // Should have read some data
    });

    test('IsarWriter should handle sequential vs random write patterns', () {
      final buffer = Uint8List(400000);
      final writer = IsarWriterImpl(buffer, 400000);
      
      // Sequential writes
      var stopwatch = Stopwatch()..start();
      for (int i = 0; i < 100000; i++) {
        writer.writeInt(i * 4, i);
      }
      stopwatch.stop();
      final sequentialTime = stopwatch.elapsedMilliseconds;
      
      // Random writes (overwrite with new pattern)
      final randomIndices = List.generate(100000, (i) => (i * 7919) % 100000);
      stopwatch = Stopwatch()..start();
      for (int i = 0; i < 100000; i++) {
        writer.writeInt(randomIndices[i] * 4, i + 1000000);
      }
      stopwatch.stop();
      final randomTime = stopwatch.elapsedMilliseconds;
      
      print('Sequential write (100k): ${sequentialTime}ms');
      print('Random write (100k): ${randomTime}ms');
      
      // Both should be reasonably fast
      expect(sequentialTime, lessThan(1000));
      expect(randomTime, lessThan(1000));
      
      // Verify some data
      final reader = IsarReaderImpl(buffer);
      expect(reader.readInt(0), greaterThanOrEqualTo(1000000));
    });

    test('IsarReader should handle memory efficiency with large datasets', () {
      final buffer = Uint8List(4000000); // 4MB buffer
      final writer = IsarWriterImpl(buffer, 4000000);
      
      // Write 1M integers
      final stopwatch = Stopwatch()..start();
      for (int i = 0; i < 1000000; i++) {
        writer.writeInt(i * 4, i);
      }
      stopwatch.stop();
      final writeTime = stopwatch.elapsedMilliseconds;
      
      // Read every 1000th integer
      final reader = IsarReaderImpl(buffer);
      stopwatch.reset();
      stopwatch.start();
      
      var sum = 0;
      for (int i = 0; i < 1000000; i += 1000) {
        sum += reader.readInt(i * 4);
      }
      
      stopwatch.stop();
      final readTime = stopwatch.elapsedMilliseconds;
      
      print('Large dataset write (1M ints): ${writeTime}ms');
      print('Large dataset sparse read (1k samples): ${readTime}ms');
      
      expect(writeTime, lessThan(2000));
      expect(readTime, lessThan(100));
      expect(sum, equals(499500000)); // Sum of 0, 1000, 2000, ..., 999000
    });
  });
}
