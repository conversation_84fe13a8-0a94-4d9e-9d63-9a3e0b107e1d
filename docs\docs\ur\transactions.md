---
title: لین دین
---

# لین دین

ای زار میں، لین دین کام کی ایک اکائی میں متعدد ڈیٹا بیس آپریشنز کو یکجا کرتا ہے۔ اسر کے ساتھ زیادہ تر تعاملات لین دین کا استعمال کرتے ہیں۔ اسار میں پڑھنے اور لکھنے کی رسائی [ACID](http://en.wikipedia.org/wiki/ACID) کے مطابق ہے۔ اگر کوئی غلطی ہوتی ہے تو ٹرانزیکشنز خود بخود واپس ہو جاتی ہیں۔

## واضح لین دین

ایک واضح لین دین میں، آپ کو ڈیٹا بیس کا ایک مستقل سنیپ شاٹ ملتا ہے۔ لین دین کی مدت کو کم سے کم کرنے کی کوشش کریں۔ لین دین میں نیٹ ورک کالز یا دیگر طویل عرصے سے چلنے والے آپریشنز کرنا منع ہے۔

لین دین (خاص طور پر لین دین لکھیں) کی ایک قیمت ہوتی ہے، اور آپ کو ہمیشہ ایک ہی لین دین میں یکے بعد دیگرے آپریشنز کو گروپ کرنے کی کوشش کرنی چاہیے۔

لین دین یا تو مطابقت پذیر یا غیر مطابقت پذیر ہوسکتے ہیں۔ ہم وقت ساز لین دین میں، آپ صرف مطابقت پذیر کارروائیوں کا استعمال کر سکتے ہیں۔ غیر مطابقت پذیر لین دین میں، صرف اسینک آپریشنز۔

|              | Read         | Read & Write       |
|--------------|--------------|--------------------|
| Synchronous  | `.txnSync()` | `.writeTxnSync()`  |
| Asynchronous | `.txn()`     | `.writeTxn()`      |


### لین دین پڑھیں

واضح پڑھنے والے لین دین اختیاری ہیں، لیکن وہ آپ کو ایٹم ریڈز کرنے اور لین دین کے اندر موجود ڈیٹا بیس کی مستقل حالت پر انحصار کرنے کی اجازت دیتے ہیں۔ داخلی طور پر ای ذار تمام پڑھنے والے آپریشنز کے لیے ہمیشہ مضمر پڑھنے والے لین دین کا استعمال کرتا ہے۔

:::tip
اےسنک پڑھنے والے لین دین دوسرے پڑھنے اور لکھنے والے لین دین کے متوازی چلتے ہیں۔ بہت اچھا، ٹھیک ہے؟
:::

### لین دین لکھیں۔

پڑھنے کی کارروائیوں کے برعکس، اسار میں تحریری کارروائیوں کو ایک واضح لین دین میں لپیٹنا ضروری ہے۔

جب تحریری لین دین کامیابی کے ساتھ ختم ہوجاتا ہے، تو یہ خود بخود کمٹڈ ہوجاتا ہے، اور تمام تبدیلیاں ڈسک پر لکھی جاتی ہیں۔ اگر کوئی خرابی پیش آتی ہے تو، لین دین کو روک دیا جاتا ہے، اور تمام تبدیلیاں واپس کر دی جاتی ہیں۔ ٹرانزیکشنز "سب یا کچھ نہیں" ہیں: یا تو ٹرانزیکشن کے اندر تمام تحریریں کامیاب ہوتی ہیں، یا ان میں سے کوئی بھی ڈیٹا کی مستقل مزاجی کی ضمانت کے لیے اثر انداز نہیں ہوتا ہے۔

:::warning
جب ڈیٹا بیس آپریشن ناکام ہوجاتا ہے، تو لین دین ختم ہوجاتا ہے اور اسے مزید استعمال نہیں کیا جانا چاہیے۔ یہاں تک کہ اگر آپ ڈارٹ میں غلطی کو پکڑتے ہیں۔
:::

```dart
@collection
class Contact {
  Id? id;

  String? name;
}

// GOOD
await isar.writeTxn(() async {
  for (var contact in getContacts()) {
    await isar.contacts.put(contact);
  }
});

// BAD: move loop inside transaction
for (var contact in getContacts()) {
  await isar.writeTxn(() async {
    await isar.contacts.put(contact);
  });
}
```
