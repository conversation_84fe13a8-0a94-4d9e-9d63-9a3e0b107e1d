{"name": "isar", "version": "2.5.1", "description": "The web bindings for Isar, a multi-platform database for Dart.", "author": "<PERSON>", "license": "Apache-2.0", "repository": "https://github.com/isar-community/isar-web", "homepage": "https://isar-community.dev/", "main": "dist/index.js", "directories": {"test": "test"}, "scripts": {"build": "rm -rf ./out && npx tsc"}, "devDependencies": {"ts-loader": "^9.2.6", "typescript": "^4.5.5", "webpack": "^5.67.0", "webpack-cli": "^5.0.0", "fast-deep-equal": "^3.1.1", "broadcast-channel": "^4.11.0"}}