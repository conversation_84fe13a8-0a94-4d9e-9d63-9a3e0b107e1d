---
home: true
title: 主页
heroImage: /isar.svg
actions:
  - text: 让我们开始吧
    link: /zh/tutorials/quickstart.html
    type: primary
features:
  - title: 💙 专门为 Flutter 打造
    details: 简化设置，易于使用，几行代码即可开始使用，无样板代码。
  - title: 🚀 高可扩展性
    details: 单个 NoSQL 数据库实例即能支持存入数十万的数据并能保证高速的异步查询。
  - title: 🍭 多功能性
    details: Isar 集成了很多现有功能来帮助你管理数据：包括但不限于复合索引和多条目索引、查询修改器、支持 JSON 等。
  - title: 🔎 全文检索
    details: Isar 内部支持全文检索。创建一个多条目索引然后进行查询将变得十分简单。
  - title: 🧪 ACID 语义
    details: Isar 兼容 ACID 语义并能自动处理事务。倘若遇到错误会自动回滚。
  - title: 💃 类型静态
    details: Isar 的查询都是静态的，即在编译时就已确定变量。完全无需担心运行时错误。
  - title: 📱 支持多平台
    details: iOS、 Android、桌面端以及 Web 端！
  - title: ⏱ 异步多线程
    details: 并行查询 & 多线程支持开箱即用
  - title: 🦄 开源
    details: 所有一切都是开源并永久免费！

footer: Apache Licensed | Copyright © 2022 Simon Leier
---
