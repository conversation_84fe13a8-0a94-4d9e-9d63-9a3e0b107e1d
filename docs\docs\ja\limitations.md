# 制限事項

ご存知のように、Isarはモバイル端末や VM上で動作するデスクトップ、そしてWeb上で動作します。この2つのプラットフォームは非常に異なっており、それぞれ異なる制限事項があります。

## VMの制限事項

- 文字列の最初の1024バイトのみがwhere節の接頭辞として使用可能です。
- オブジェクトのサイズは 16MB までとなります。

## Webの制限事項

Isar WebはIndexedDBに依存しているため、より多くの制限事項がありますが、 Isarを使用している間はほとんど気にはならないでしょう。

- 同期メソッドはサポートされていません。
- 現時点において、 `Isar.splitWords()`と`.matches()`フィルターは未実装です。
- スキーマの変更はVMほど厳密にはチェックされないので、規則に従うように注意してください。
- すべての数値型は double (唯一の js 数値型) として保存されるので、 `@Size32` は影響しません。
- インデックスの表現が異なるため、Hashインデックスの容量は減りません。(機能/動作は同じです）
- `col.delete()` と `col.deleteAll()` は正しく動作しますが、戻り値が正しくありません。
- `col.clear()` はオートインクリメント値をリセットしません。
- 値として `NaN` はサポートされていません。
