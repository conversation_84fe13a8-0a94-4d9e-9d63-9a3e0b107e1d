<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1.57291,0,0,1.57291,-1016.56,-960.65)">
        <g transform="matrix(-161.846,280.399,280.399,161.846,831.677,599.375)">
            <path d="M0.251,-0.225L0.251,-0.225C0.4,-0.311 0.59,-0.317 0.751,-0.225C0.952,-0.108 1.042,0.128 0.982,0.343C0.975,0.32 0.965,0.298 0.953,0.277C0.854,0.107 0.635,0.048 0.465,0.146C0.415,0.175 0.358,0.183 0.302,0.168C0.247,0.153 0.201,0.118 0.172,0.068C0.144,0.019 0.136,-0.039 0.151,-0.094C0.156,-0.113 0.163,-0.13 0.172,-0.146C0.191,-0.179 0.218,-0.205 0.251,-0.225Z" style="fill:url(#_Linear1);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-161.846,280.399,280.399,161.846,948.497,666.803)">
            <path d="M0.049,-0.277C0.096,-0.195 0.173,-0.136 0.266,-0.111C0.358,-0.086 0.454,-0.099 0.536,-0.147C0.639,-0.206 0.77,-0.17 0.829,-0.068C0.858,-0.019 0.865,0.039 0.85,0.094C0.836,0.15 0.8,0.196 0.751,0.224L0.751,0.225C0.601,0.311 0.411,0.317 0.251,0.225C0.049,0.108 -0.041,-0.128 0.019,-0.342C0.027,-0.32 0.037,-0.298 0.049,-0.277Z" style="fill:url(#_Linear2);fill-rule:nonzero;"/>
        </g>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1,0,0,-1,0,0.0254517)"><stop offset="0" style="stop-color:rgb(0,238,255);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(125,88,255);stop-opacity:1"/></linearGradient>
        <linearGradient id="_Linear2" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1,0,0,-1,0,-0.025463)"><stop offset="0" style="stop-color:rgb(0,238,255);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(125,88,255);stop-opacity:1"/></linearGradient>
    </defs>
</svg>
