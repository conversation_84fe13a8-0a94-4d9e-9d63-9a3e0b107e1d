[target.aarch64-linux-android]
rustflags = [
  "-C", "link-arg=-Wl,--hash-style=both",
  "-C", "link-arg=-Wl,-z,max-page-size=16384"
]
[target.armv7-linux-androideabi]
rustflags = [
  "-C", "link-arg=-Wl,--hash-style=both", 
  "-C", "link-arg=-Wl,-z,max-page-size=16384"
]

[target.x86_64-linux-android]
rustflags = [
  "-C", "link-arg=-Wl,--hash-style=both",
  "-C", "link-arg=-Wl,-z,max-page-size=16384"
]

[target.i686-linux-android]
rustflags = [
  "-C", "link-arg=-Wl,--hash-style=both",
  "-C", "link-arg=-Wl,-z,max-page-size=16384"
]