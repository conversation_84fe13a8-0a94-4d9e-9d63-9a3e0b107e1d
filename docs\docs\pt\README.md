---
home: true
title: Home
heroImage: /isar.svg
actions:
  - text: Vamos Começar!
    link: /pt/tutorials/quickstart.html
    type: primary
features:
  - title: 💙 Feito para Flutter
    details: Configuração mínima, fácil de usar, sem configuração, sem clichê. Basta adicionar algumas linhas de código para começar.
  - title: 🚀 Altamente escalável
    details: Armazene centenas de milhares de registros em um único banco de dados NoSQL e consulte-os de forma eficiente e assíncrona.
  - title: 🍭 Rico em recursos
    details: O Isar possui um rico conjunto de recursos para ajudá-lo a gerenciar seus dados. Índices compostos e de várias entradas, modificadores de consulta, suporte a JSON e muito mais.
  - title: 🔎 Pesquisa de texto completo
    details: Isar tem busca embutida de texto completo. Crie um índice de várias entradas e pesquise registros facilmente.
  - title: 🧪 Semântica ACID
    details: Isar é compatível com ACID e lida com transações automaticamente. Ele reverte as alterações se ocorrer um erro.
  - title: 💃 Tipagem estática
    details: As consultas de Isar são estaticamente tipadas e verificadas em tempo de compilação. Não há necessidade de se preocupar com erros de tempo de execução.
  - title: 📱 Multi plataforma
    details: iOS, Android, Desktop e SUPORTE COMPLETO DA WEB!
  - title: ⏱ Assíncrono
    details: Operações de consulta paralela e suporte multiisolado pronto para uso
  - title: 🦄 Código Aberto
    details: Tudo é de código aberto e gratuito para sempre!

footer: Apache Licensed | Copyright © 2022 Simon Leier
---
