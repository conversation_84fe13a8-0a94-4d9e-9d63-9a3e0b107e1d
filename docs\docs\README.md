---
home: true
title: Home
heroImage: /isar.svg
actions:
  - text: Let's Get Started!
    link: /tutorials/quickstart.html
    type: primary
features:
  - title: 💙 Made for Flutter
    details: Minimal setup, Easy to use, no config, no boilerplate. Just add a few lines of code to get started.
  - title: 🚀 Highly scalable
    details: Store hundreds of thousands of records in a single NoSQL database and query them efficiently and asynchronously.
  - title: 🍭 Feature-rich
    details: Isar has a rich set of features to help you manage your data. Composite & multi-entry indexes, query modifiers, JSON support, and more.
  - title: 🔎 Full-text search
    details: <PERSON><PERSON> has built-in full-text search. Create a multi-entry index and search for records easily.
  - title: 🧪 ACID semantics
    details: Isa<PERSON> is ACID compliant and handles transactions automatically. It rolls back changes if an error occurs.
  - title: 💃 Static typing
    details: Isa<PERSON>'s queries are statically typed and compile-time checked. No need to worry about runtime errors. 
  - title: 📱 Multiplatform
    details: iOS, Android, Desktop, and FULL WEB SUPPORT!
  - title: ⏱ Asynchronous
    details: Parallel query operations & multi-isolate support out-of-the-box
  - title: 🦄 Open Source
    details: Everything is open source and free forever!

footer: Apache Licensed | Copyright © 2022 <PERSON>
---
