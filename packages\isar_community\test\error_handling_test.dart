@TestOn('vm')

import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:isar_community/isar.dart';
import 'package:isar_community/src/native/isar_core.dart';
import 'package:isar_community/src/native/isar_reader_impl.dart';
import 'package:isar_community/src/native/isar_writer_impl.dart';
import 'package:test/test.dart';

void main() {
  group('Error Handling Tests', () {
    test('IsarReader should handle buffer boundary conditions', () {
      // Test with minimal buffer
      final smallBuffer = Uint8List(4);
      final writer = IsarWriterImpl(smallBuffer, 4);
      writer.writeInt(0, 42);
      
      final reader = IsarReaderImpl(smallBuffer);
      expect(reader.readInt(0), equals(42));
      
      // Test reading beyond buffer should not crash (implementation dependent)
      expect(() => reader.readInt(4), returnsNormally);
    });

    test('IsarWriter should handle buffer overflow gracefully', () {
      final buffer = Uint8List(10);
      final writer = IsarWriterImpl(buffer, 10);
      
      // Write data that fits
      writer.writeInt(0, 123);
      writer.writeInt(4, 456);
      
      // Attempt to write beyond buffer capacity
      expect(() => writer.writeInt(8, 789), returnsNormally);
      
      // Verify existing data is still intact
      final reader = IsarReaderImpl(buffer);
      expect(reader.readInt(0), equals(123));
      expect(reader.readInt(4), equals(456));
    });

    test('IsarReader should handle corrupted data gracefully', () {
      final buffer = Uint8List(100);
      
      // Fill with random/corrupted data
      for (int i = 0; i < buffer.length; i++) {
        buffer[i] = (i * 17 + 42) % 256;
      }
      
      final reader = IsarReaderImpl(buffer);
      
      // Reading corrupted data should not crash
      expect(() => reader.readInt(0), returnsNormally);
      expect(() => reader.readLong(8), returnsNormally);
      expect(() => reader.readFloat(16), returnsNormally);
      expect(() => reader.readDouble(24), returnsNormally);
      expect(() => reader.readBool(32), returnsNormally);
    });

    test('IsarWriter should handle null string operations', () {
      final buffer = Uint8List(100);
      final writer = IsarWriterImpl(buffer, 100);
      
      // Write null string as byte list
      writer.writeByteList(0, null);
      
      final reader = IsarReaderImpl(buffer);
      final result = reader.readByteList(0);
      
      expect(result, isNull);
    });

    test('IsarReader should handle empty string operations', () {
      final buffer = Uint8List(100);
      final writer = IsarWriterImpl(buffer, 100);
      
      // Write empty string
      final emptyBytes = utf8.encode('');
      writer.writeByteList(0, Uint8List.fromList(emptyBytes));
      
      final reader = IsarReaderImpl(buffer);
      final result = reader.readByteList(0);
      
      expect(result, isNotNull);
      expect(result!.length, equals(0));
      expect(utf8.decode(result), equals(''));
    });

    test('IsarWriter should handle very long strings', () {
      final buffer = Uint8List(10000);
      final writer = IsarWriterImpl(buffer, 10000);
      
      // Create a very long string
      final longString = 'A' * 5000;
      final longBytes = utf8.encode(longString);
      
      writer.writeByteList(0, Uint8List.fromList(longBytes));
      
      final reader = IsarReaderImpl(buffer);
      final result = reader.readByteList(0);
      
      expect(result, isNotNull);
      expect(utf8.decode(result!), equals(longString));
    });

    test('IsarReader should handle special Unicode characters', () {
      final buffer = Uint8List(1000);
      final writer = IsarWriterImpl(buffer, 1000);
      
      // Test various Unicode characters
      final unicodeStrings = [
        'Hello 世界', // Chinese
        'Здравствуй мир', // Russian
        'مرحبا بالعالم', // Arabic
        '🌍🌎🌏', // Emojis
        'Iñtërnâtiônàlizætiøn', // Accented characters
        '', // Empty string
        ' ', // Space
        '\n\t\r', // Whitespace characters
      ];
      
      var offset = 0;
      for (final str in unicodeStrings) {
        final bytes = utf8.encode(str);
        writer.writeByteList(offset, Uint8List.fromList(bytes));
        offset += 100; // Reserve space between strings
      }
      
      final reader = IsarReaderImpl(buffer);
      offset = 0;
      
      for (final expectedStr in unicodeStrings) {
        final bytes = reader.readByteList(offset);
        final actualStr = bytes != null ? utf8.decode(bytes) : null;
        expect(actualStr, equals(expectedStr));
        offset += 100;
      }
    });

    test('IsarWriter should handle extreme numeric values', () {
      final buffer = Uint8List(100);
      final writer = IsarWriterImpl(buffer, 100);
      
      // Test extreme values
      writer.writeInt(0, 0x7FFFFFFF); // Max int32
      writer.writeInt(4, -0x80000000); // Min int32
      writer.writeLong(8, 0x7FFFFFFFFFFFFFFF); // Max int64
      writer.writeLong(16, -0x8000000000000000); // Min int64
      writer.writeFloat(24, 3.4028235e+38); // Max float
      writer.writeFloat(28, -3.4028235e+38); // Min float
      writer.writeDouble(32, 1.7976931348623157e+308); // Max double
      writer.writeDouble(40, -1.7976931348623157e+308); // Min double
      
      final reader = IsarReaderImpl(buffer);
      
      expect(reader.readInt(0), equals(0x7FFFFFFF));
      expect(reader.readInt(4), equals(-0x80000000));
      expect(reader.readLong(8), equals(0x7FFFFFFFFFFFFFFF));
      expect(reader.readLong(16), equals(-0x8000000000000000));
      expect(reader.readFloat(24), closeTo(3.4028235e+38, 1e30));
      expect(reader.readFloat(28), closeTo(-3.4028235e+38, 1e30));
      expect(reader.readDouble(32), closeTo(1.7976931348623157e+308, 1e300));
      expect(reader.readDouble(40), closeTo(-1.7976931348623157e+308, 1e300));
    });

    test('IsarReader should handle special float values', () {
      final buffer = Uint8List(50);
      final writer = IsarWriterImpl(buffer, 50);
      
      // Write special float values
      writer.writeFloat(0, double.infinity);
      writer.writeFloat(4, double.negativeInfinity);
      writer.writeFloat(8, double.nan);
      writer.writeFloat(12, 0.0);
      writer.writeFloat(16, -0.0);
      
      writer.writeDouble(20, double.infinity);
      writer.writeDouble(28, double.negativeInfinity);
      writer.writeDouble(36, double.nan);
      
      final reader = IsarReaderImpl(buffer);
      
      expect(reader.readFloat(0), equals(double.infinity));
      expect(reader.readFloat(4), equals(double.negativeInfinity));
      expect(reader.readFloat(8), isNaN);
      expect(reader.readFloat(12), equals(0.0));
      expect(reader.readFloat(16), equals(-0.0));
      
      expect(reader.readDouble(20), equals(double.infinity));
      expect(reader.readDouble(28), equals(double.negativeInfinity));
      expect(reader.readDouble(36), isNaN);
    });

    test('IsarWriter should handle malformed list data', () {
      final buffer = Uint8List(200);
      final writer = IsarWriterImpl(buffer, 200);
      
      // Test with lists containing null elements
      final mixedList = ['valid', null, 'string', null];
      writer.writeStringList(0, mixedList);
      
      final reader = IsarReaderImpl(buffer);
      final result = reader.readStringOrNullList(0);
      
      expect(result, equals(mixedList));
    });

    test('IsarReader should handle concurrent access patterns safely', () {
      final buffer = Uint8List(1000);
      final writer = IsarWriterImpl(buffer, 1000);
      
      // Write test data
      for (int i = 0; i < 100; i++) {
        writer.writeInt(i * 4, i);
      }
      
      // Create multiple readers
      final readers = List.generate(5, (_) => IsarReaderImpl(buffer));
      
      // All readers should see the same data
      for (int i = 0; i < 100; i++) {
        final expectedValue = i;
        for (final reader in readers) {
          expect(reader.readInt(i * 4), equals(expectedValue));
        }
      }
    });

    test('IsarWriter should handle rapid successive writes', () {
      final buffer = Uint8List(1000);
      final writer = IsarWriterImpl(buffer, 1000);
      
      // Perform rapid writes
      for (int iteration = 0; iteration < 10; iteration++) {
        for (int i = 0; i < 100; i++) {
          writer.writeInt(i * 4, iteration * 100 + i);
        }
      }
      
      // Verify final state
      final reader = IsarReaderImpl(buffer);
      for (int i = 0; i < 100; i++) {
        final expectedValue = 9 * 100 + i; // Last iteration values
        expect(reader.readInt(i * 4), equals(expectedValue));
      }
    });

    test('IsarReader should handle memory pressure scenarios', () {
      // Test with large buffer to simulate memory pressure
      final largeBuffer = Uint8List(100000);
      final writer = IsarWriterImpl(largeBuffer, 100000);
      
      // Fill buffer with pattern
      for (int i = 0; i < 25000; i++) {
        writer.writeInt(i * 4, i % 1000);
      }
      
      final reader = IsarReaderImpl(largeBuffer);
      
      // Verify pattern integrity
      for (int i = 0; i < 25000; i++) {
        expect(reader.readInt(i * 4), equals(i % 1000));
      }
    });

    test('IsarWriter should handle zero-length operations', () {
      final buffer = Uint8List(100);
      final writer = IsarWriterImpl(buffer, 100);
      
      // Write zero-length lists
      writer.writeBoolList(0, []);
      writer.writeIntList(10, []);
      writer.writeStringList(20, []);
      writer.writeByteList(30, Uint8List(0));
      
      final reader = IsarReaderImpl(buffer);
      
      expect(reader.readBoolList(0), isEmpty);
      expect(reader.readIntList(10), isEmpty);
      expect(reader.readStringList(20), isEmpty);
      expect(reader.readByteList(30), isEmpty);
    });
  });
}
