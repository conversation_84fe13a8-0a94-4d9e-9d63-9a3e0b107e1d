---
home: true
title: Home
heroImage: /isar.svg
actions:
  - text: Empecemos!
    link: /es/tutorials/quickstart.html
    type: primary
features:
  - title: 💙 Hecho para Flutter
    details: Mínima inicialización, fácil de usar, sin configuración, sin repetición. Solo agrega algunas líneas de código para comenzar.
  - title: 🚀 Altamente escalable
    details: Almacena cientos de miles de registros en una sola base de datos NoSQL y consúltalos de forma eficiente y asíncrona.
  - title: 🍭 Múltiples características
    details: Isar posee una gran cantidad de características para ayudarte a administrar tus datos. Índices compuestos y multi-entrada, modificadores de consultas, soporte para JSON, y mucho más.
  - title: 🔎 Búsqueda de texto completo
    details: Isar tiene incorporado un buscador de texto completo. Crea un índice multi-entrada y busca texto de forma fácil.
  - title: 🧪 Semántica ACID
    details: Isar es compatible con ACID y maneja las transacciones automáticamente. Retrocede los cambios en caso de error.
  - title: 💃 Tipeado estático
    details: Las consultas de Isar son tipeadas estáticamente y verificadas en tiempo de compilación. No hay necesidad de preocuparse por errores en tiempo de ejecución.
  - title: 📱 Multiplataforma
    details: Soporte completo para iOS, Android, Desktop, WEB!
  - title: ⏱ Asíncrona
    details: Isar incluye operaciones de consulta en paralelo y soporte multi-isolate.
  - title: 🦄 Código abierto
    details: Completamente de código abierto y libre para siempre!

footer: Apache Licensed | Copyright © 2022 Simon Leier
---
