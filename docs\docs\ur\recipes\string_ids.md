---
title: اسٹرنگ آئی ڈیز
---

# اسٹرنگ آئی ڈیز

یہ مجھے ملنے والی اکثر درخواستوں میں سے ایک ہے، اس لیے یہاں اسٹرنگ آئی ڈیز کے استعمال سے متعلق ایک سبق ہے۔

ایزار مقامی طور پر اسٹرنگ آئی ڈیز کی حمایت نہیں کرتا ہے، اور اس کی ایک اچھی وجہ ہے: انٹیجر آئی ڈیز بہت زیادہ موثر اور تیز ہیں۔ خاص طور پر لنکس کے لیے، اسٹرنگ آئی ڈی کا اوور ہیڈ بہت اہم ہے۔

میں سمجھتا ہوں کہ بعض اوقات آپ کو بیرونی ڈیٹا ذخیرہ کرنا پڑتا ہے جو UUIDs یا دیگر غیر عددی آئی ڈیز استعمال کرتا ہے۔ میں اسٹرنگ آئی ڈی کو آپ کے آبجیکٹ میں بطور پراپرٹی اسٹور کرنے اور 64 بٹ انٹ بنانے کے لیے تیز رفتار ہیش نفاذ کا استعمال کرنے کی تجویز کرتا ہوں جسے بطور آئی ڈی استعمال کیا جا سکتا ہے۔

```dart
@collection
class User {
  String? id;

  Id get isarId => fastHash(id!);

  String? name;

  int? age;
}
```

اس نقطہ نظر کے ساتھ، آپ کو دونوں جہانوں میں سے بہترین حاصل ہوتا ہے: لنکس کے لیے موثر عددی ڈیز اور اسٹرنگ آئی ڈیز استعمال کرنے کی اہلیت۔

## فاسٹ ہیش فنکشن

مثالی طور پر، آپ کے ہیش فنکشن میں اعلیٰ معیار ہونا چاہیے (آپ کو تصادم نہیں چاہیے) اور تیز ہونا چاہیے۔ میں مندرجہ ذیل نفاذ کو استعمال کرنے کی سفارش کرتا ہوں:

```dart
/// FNV-1a 64bit hash algorithm optimized for Dart Strings
int fastHash(String string) {
  var hash = 0xcbf29ce484222325;

  var i = 0;
  while (i < string.length) {
    final codeUnit = string.codeUnitAt(i++);
    hash ^= codeUnit >> 8;
    hash *= 0x100000001b3;
    hash ^= codeUnit & 0xFF;
    hash *= 0x100000001b3;
  }

  return hash;
}
```
اگر آپ ایک مختلف ہیش فنکشن کا انتخاب کرتے ہیں، تو یقینی بنائیں کہ یہ 64 بٹ انٹ واپس کرتا ہے اور کرپٹوگرافک ہیش فنکشن استعمال کرنے سے گریز کریں کیونکہ وہ بہت سست ہیں۔

:::warning
Avoid using `string.hashCode` because it is not guaranteed to be stable across different platforms and versions of Dart.
:::