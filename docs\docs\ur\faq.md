---
title: اکثر پوچھے گئے سوالات
---

# اکثر پوچھے گئے سوالات

ای زار اور فلٹر ڈیٹا بیس کے بارے میں اکثر پوچھے جانے والے سوالات کا بے ترتیب مجموعہ۔

### مجھے ڈیٹا بیس کی ضرورت کیوں ہے؟

> میں اپنا ڈیٹا بیک اینڈ ڈیٹا بیس میں محفوظ کرتا ہوں، مجھے اسار کی ضرورت کیوں ہے؟

آج بھی، اگر آپ سب وے یا ہوائی جہاز میں ہیں یا اگر آپ اپنی دادی سے ملنے جاتے ہیں، جن کے پاس وائی فائی نہیں ہے اور سیل سگنل بہت خراب ہے۔ آپ کو خراب کنکشن کو اپنی ایپ کو معذور نہیں ہونے دینا چاہیے!

### Isar vs Hive

The answer is easy: Isar was [started as a replacement for Hive](https://github.com/hivedb/hive/issues/246) and is now at a state where I recommend always using Isar over Hive.

### کہاں کی شقیں؟!

> **_I_** کو یہ کیوں منتخب کرنا ہوگا کہ کون سا انڈیکس استعمال کرنا ہے؟

متعدد وجوہات ہیں۔ بہت سے ڈیٹا بیس دی گئی استفسار کے لیے بہترین انڈیکس کا انتخاب کرنے کے لیے ہیورسٹکس کا استعمال کرتے ہیں۔ ڈیٹا بیس کو اضافی استعمال کا ڈیٹا جمع کرنے کی ضرورت ہے (-> اوور ہیڈ) اور پھر بھی غلط انڈیکس کا انتخاب کر سکتا ہے۔ یہ استفسار کی تخلیق کو بھی سست بناتا ہے۔

آپ کے ڈیٹا کو آپ سے بہتر کوئی نہیں جانتا، ڈویلپر۔ لہذا آپ بہترین انڈیکس کا انتخاب کر سکتے ہیں اور مثال کے طور پر فیصلہ کر سکتے ہیں کہ آیا آپ استفسار یا چھانٹنے کے لیے انڈیکس استعمال کرنا چاہتے ہیں۔

### کیا مجھے اشاریہ جات / جہاں شقیں استعمال کرنی ہیں؟

نھیں کیا! اگر آپ صرف فلٹرز پر بھروسہ کرتے ہیں تو اسر کافی تیز ہے۔

### کیا اسر کا روزہ کافی ہے؟

Isar موبائل کے لیے تیز ترین ڈیٹا بیس میں سے ایک ہے، اس لیے اسے زیادہ تر استعمال کے معاملات کے لیے کافی تیز ہونا چاہیے۔ اگر آپ کارکردگی کے مسائل کا شکار ہیں، تو امکان یہ ہے کہ آپ کچھ غلط کر رہے ہیں۔

### کیا اسر میری ایپ کا سائز بڑھاتا ہے؟

A little bit, yes. Isar will increase the download size of your app by about 1 - 1.5 MB. Isar Web adds only a few KB.

### دستاویزات غلط ہیں / ٹائپنگ کی غلطی ہے۔

Oh no, sorry. Please [open an issue](https://github.com/isar-community/isar/issues/new/choose) or, even better, a PR to fix it 💪.
