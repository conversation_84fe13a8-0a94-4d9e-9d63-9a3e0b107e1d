# 局限性

你知道 Isar 是跨平台支持移动端、桌面端和 Web 端的，在移动和桌面端它是通过虚拟机来运行的，而 Web 端则不是。两者差异较大且有不同的局限性。

## 虚拟机的局限

- 一个字符串只能用其前 1024 字节来进行 Where 子句的前缀匹配查询
- 对象的大小只能为 16MB

## Web 端的局限

因为 Isar 在 Web 端依赖于 IndexedDB，所以遇到的限制会更多，但即便如此，在使用 Isar 的过程中基本可以忽略不计。

- 不支持同步操作方法
- 目前，`Isar.splitWords()` 和 `.matches()` 还不支持 Web 端
- 不会像在虚拟机中那样严格检查 Schema 的改变，所以必须谨慎对待
- 所有数字类型将按照双浮点数（JS 中唯一的数字类型）做排序，所以 `@Size32` 不会起作用
- 索引的原理不同，所以哈希索引无法节省更多存储空间（但用法依然一致）
- `col.delete()` 和 `col.deleteAll()` 能运行但是返回值不正确
- `col.clear()` 无法重置自增值
- `NaN` 尚不支持
