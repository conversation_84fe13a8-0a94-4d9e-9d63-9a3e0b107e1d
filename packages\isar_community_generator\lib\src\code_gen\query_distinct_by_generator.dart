import 'package:dartx/dartx.dart';
import 'package:isar_community/isar.dart';
import 'package:isar_community_generator/src/isar_type.dart';
import 'package:isar_community_generator/src/object_info.dart';

String generateDistinctBy(ObjectInfo oi) {
  var code = '''
  extension ${oi.dartName}QueryWhereDistinct on QueryBuilder<${oi.dartName}, ${oi.dartName}, QDistinct> {''';
  for (final property in oi.objectProperties) {
    if (property.isarType == IsarType.string) {
      code += '''
        QueryBuilder<${oi.dartName}, ${oi.dartName}, QDistinct>distinctBy${property.dartName.capitalize()}({bool caseSensitive = true}) {
          return QueryBuilder.apply(this, (query) {
            return query.addDistinctBy(r'${property.isarName}', caseSensitive: caseSensitive);
          });
        }''';
    } else if (!property.isarType.containsObject) {
      code += '''
        QueryBuilder<${oi.dartName}, ${oi.dartName}, QDistinct>distinctBy${property.dartName.capitalize()}() {
          return QueryBuilder.apply(this, (query) {
            return query.addDistinctBy(r'${property.isarName}');
          });
        }''';
    }
  }
  return '$code}';
}
