---
home: true
title: ホーム
heroImage: /isar.svg
actions:
  - text: さっそく始めよう！
    link: /ja/tutorials/quickstart.html
    type: primary
features:
  - title: 💙 Flutterのために
    details: 最小限のSetup、簡単に使えて、追加の設定やボイラープレートは不要。数行のコードを追加後にすぐに使用可能。
  - title: 🚀 高い拡張性
    details: 数十万件のレコードを1つのNoSQLデータベースに格納し、効率的かつ非同期にクエリを実行。
  - title: 🍭 豊富な機能
    details: 複合 & 複数条件対応インデックスやクエリ修飾子、JSONのサポートなど、データ管理を支援する豊富な機能を搭載。
  - title: 🔎 全文検索機能
    details: 全文検索機能を保持。複数の条件を設定したIndexを作成し、簡単にレコードを検索する事が可能。
  - title: 🧪 ACID セマンティクス
    details: IsarはACIDに準拠しており、トランザクションを自動的に処理。エラーが発生しても変更をロールバック。
  - title: 💃 静的型付け
    details: Isarのクエリは静的型付けされ、コンパイル時にチェックされます。実行時エラーを心配する必要はありません。
  - title: 📱 マルチプラットフォーム対応
    details: iOS, Android, Desktop, そしてWEBにも対応！
  - title: ⏱️ 非同期処理
    details: 並列クエリ操作とMulti-Isolateをすぐに利用可能。
  - title: 🦄 オープンソース
    details: すべてがオープンソースで、永久に無料！

footer: Apache Licensed | Copyright © 2022 Simon Leier
---
