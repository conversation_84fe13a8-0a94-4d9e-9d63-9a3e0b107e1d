---
home: true
title: 홈
heroImage: /isar.svg
actions:
  - text: 시작하기!
    link: /tutorials/quickstart.html
    type: primary
features:
  - title: 💙 플러터를 위해 만들었어요
    details: 설정은 최소로, 사용하기 쉽고, 구성도 없고, 보일러 플레이트도 없습니다. 코드 몇 줄만 추가하면 바로 시작할 수 있습니다.
  - title: 🚀 뛰어난 확장성
    details: 수십만 개의 레코드를 단일 NoSQL 데이터베이스에 저장하고 효율적이고 비동기적으로 쿼리할 수 있습니다.
  - title: 🍭 풍부한 기능
    details: Isar에는 데이터를 관리하기 위한 다양한 기능이 있습니다. 복합 & 다중 항목 인덱스, 쿼리 수정자, JSON 지원 등이 있습니다.
  - title: 🔎 전체 텍스트 검색
    details: Isar는 전체 텍스트 검색 기능이 내장되어 있습니다. 다중 항목 색인을 작성하고 레코드를 쉽게 검색할 수 있습니다.
  - title: 🧪 ACID 시멘틱
    details: Isar는 ACID를 준수하며 트랜잭션을 자동으로 처리합니다. 오류가 발생하면 변경 내용을 롤백합니다.
  - title: 💃 정적 타입
    details: Isar의 쿼리는 정적 타입이고, 컴파일 시간에 검사됩니다. 런타임 오류에 대해 걱정할 필요가 없습니다.
  - title: 📱 다중 플랫폼
    details: iOS, Android, Desktop 및 완전한 웹 지원!
  - title: ⏱ 비동기
    details: 병렬 쿼리 작업 및 다중 Isolate 지원을 즉시 사용할 수 있습니다.
  - title: 🦄 오픈 소스입니다.
    details: 모든 것이 오픈 소스이며 영원히 무료입니다.

footer: Apache Licensed | Copyright © 2022 Simon Leier
---
