[package]
name = "isar"
version = "0.0.0"
authors = ["<PERSON> <simon<PERSON><EMAIL>>"]
edition = "2021"

[dependencies]
isar-core = { path = "../isar_core" }
threadpool = "1.8.1"
once_cell = "1.10.0"
serde_json = "1.0"
paste = "1.0"
unicode-segmentation = "1.9.0"
intmap = "2.0.0"
itertools = "0.10.3"

[target.'cfg(any(target_os = "ios", target_os = "macos"))'.dependencies]
objc = "0.2.7"
objc-foundation = "0.1.1"

[target.'cfg(target_os = "android")'.dependencies]
jni = "0.20.0"
ndk-context = "0.1.1"
once_cell = "1.10.0"

[target.'cfg(not(any(target_os = "ios", target_os = "macos", target_os = "android")))'.dependencies]
dirs = "4.0.0"

[lib]
crate-type=["staticlib", "cdylib"]
