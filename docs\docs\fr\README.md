---
home: true
title: Acceuil
heroImage: /isar.svg
actions:
  - text: Commençons !
    link: /fr/tutorials/quickstart.html
    type: primary
features:
  - title: 💙 Fait pour Flutter
    details: Configuration minimale, facilité d'utilisation. Il suffit d'ajouter quelques lignes de code pour commencer.
  - title: 🚀 Hautement extensible
    details: Stockez des centaines de milliers d'entrées dans une seule base de données NoSQL et filtrer-les de manière efficace et asynchrone.
  - title: 🍭 Riche en fonctionnalités
    details: Isar dispose d'un riche ensemble de fonctionnalités pour vous aider à gérer vos données. Index composés et multi-entrées, modificateurs de requête, support JSON, etc.
  - title: 🔎 Recherche plein texte
    details: Isar dispose d'une recherche plein texte intégrée. Créez un index à entrées multiples et recherchez facilement des entrées.
  - title: 🧪 Sémantique ACID
    details: Isar est conforme à la norme ACID et gère les transactions automatiquement. Il annule les modifications si une erreur se produit.
  - title: 💃 Types statiques
    details: Les requêtes d'Isar sont typées statiquement et vérifiées à la compilation. Pas besoin de se soucier des erreurs d'exécution.
  - title: 📱 Multiplatforme
    details: Support pour iOS, Android, Desktop et WEB!
  - title: ⏱ Asynchrone
    details: Opérations de requête parallèles et support multi-Isolate prêts à l'emploi.
  - title: 🦄 Open Source
    details: Tout est open source et gratuit pour toujours!

footer: Apache Licensed | Copyright © 2022 Simon Leier
---
