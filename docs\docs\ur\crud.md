---
title: بنائیں، پڑھیں، اپ ڈیٹ کریں، حذف کریں
---

# بنائیں، پڑھیں، اپ ڈیٹ کریں، حذف کریں

جب آپ نے اپنے کلیکشنز کی وضاحت کی ہے، تو سیکھیں کہ انہیں کیسے جوڑنا ہے!

## ای زار کھولنا

اس سے پہلے کہ آپ کچھ کر سکیں، ہمیں ای زار کی مثال درکار ہے۔ ہر مثال کے لیے لکھنے کی اجازت کے ساتھ ڈائرکٹری کی ضرورت ہوتی ہے جہاں ڈیٹا بیس فائل کو محفوظ کیا جا سکتا ہے۔ اگر آپ ڈائرکٹری کی وضاحت نہیں کرتے ہیں، تو ای زار موجودہ پلیٹ فارم کے لیے ایک مناسب ڈیفالٹ ڈائریکٹری تلاش کرے گا۔

وہ تمام اسکیمے فراہم کریں جو آپ ای زار مثال کے ساتھ استعمال کرنا چاہتے ہیں۔ اگر آپ متعدد مثالوں کو کھولتے ہیں، تو آپ کو اب بھی ہر ایک مثال کے لیے ایک ہی اسکیما فراہم کرنا ہوں گی۔

```dart
final dir = await getApplicationDocumentsDirectory();
final isar = await Isar.open(
  [RecipeSchema],
  directory: dir.path,
);
```
آپ پہلے سے طے شدہ تشکیل استعمال کرسکتے ہیں یا درج ذیل میں سے کچھ پیرامیٹرز فراہم کرسکتے ہیں۔

ترتیب | تفصیل |
| -------| -------------|
| نام | الگ الگ ناموں کے ساتھ متعدد مثالیں کھولیں۔بذریعہ ڈیفالٹ، `"ڈیفالٹ"` استعمال ہوتا ہے۔ |
|ڈائریکٹری | اس مثال کے لیے اسٹوریج کا مقام۔ بطور ڈیفالٹ، آئی او ایس کے لیے `این ایس ڈاکومینٹ ڈائریکٹری` اور اینڈرائڈ کے لیے `گٹ ڈیٹا ڈائریکٹری` استعمال کیا جاتا ہے۔ ویب کے لیے ضروری نہیں ہے۔ |
|آرام پائیدار | تحریری کارکردگی کو بڑھانے کے لیے پائیداری کی ضمانت کو آرام دیتا ہے۔ سسٹم کریش ہونے کی صورت میں (ایپ کریش نہیں)، آخری کمٹڈ ٹرانزیکشن سے محروم ہونا ممکن ہے۔ کرپشن ممکن نہیں |
| کمپیکٹ اون لانچ | یہ چیک کرنے کی شرائط کہ آیا مثال کے کھولنے پر ڈیٹا بیس کو کمپیکٹ کیا جانا چاہیے۔ |
| انسپکٹر | ڈیبگ بلڈز کے لیے انسپکٹر کو فعال کیا۔ پروفائل اور ریلیز کے لیے اس اختیار کو نظر انداز کر دیا گیا ہے۔ |

اگر کوئی مثال پہلے سے کھلی ہوئی ہے تو، 'ای زار.کھولیں()'  کو کال کرنے سے مخصوص پیرامیٹرز سے قطع نظر موجودہ مثال حاصل ہو جائے گی۔ یہ ای زار کو الگ تھلگ میں استعمال کرنے کے لیے مفید ہے۔

:::ٹپ
تمام پلیٹ فارمز پر درست راستہ حاصل کرنے کے لیے [path_provider](https://pub.dev/packages/path_provider) پیکیج استعمال کرنے پر غور کریں۔
:::

`directory/name.isar` 
ڈیٹا بیس فائل کا سٹوریج لوکیشن 
ہے۔

## ڈیٹا بیس سے پڑھنا

ای زار میں دی گئی قسم کی نئی اشیاء تلاش کرنے، استفسار کرنے اور تخلیق کرنے کے لیے `ای زار کلیکشن` مثالیں استعمال کریں۔

ذیل میں دی گئی مثالوں کے لیے، ہم فرض کرتے ہیں کہ ہمارے پاس ایک مجموعہ ہے 'رےسیپ' کی وضاحت اس طرح کی گئی ہے:

```dart
@collection
class Recipe {
  Id? id;

  String? name;

  DateTime? lastCooked;

  bool? isFavorite;
}
```

### ایک مجموعہ حاصل کریں۔

آپ کے تمام مجموعے ایزار مثال میں رہتے ہیں۔ آپ ترکیبوں کا مجموعہ اس کے ساتھ حاصل کرسکتے ہیں:

```dart
final recipes = isar.recipes;
```
یہ آسان تھا! اگر آپ کلیکشن ایکسیسرز استعمال نہیں کرنا چاہتے تو آپ `کلیکشن()` طریقہ بھی استعمال کر سکتے ہیں:

```dart
final recipes = isar.collection<Recipe>();
```

### کوئی چیز حاصل کریں (بذریعہ ID)

ہمارے پاس ابھی تک ڈیٹا جمع نہیں ہے لیکن آئیے دکھاوا کرتے ہیں کہ ہم ایسا کرتے ہیں تاکہ ہم 123 آئی ڈی کے ذریعے ایک خیالی چیز حاصل کر سکیں۔
```dart
final recipe = await isar.recipes.get(123);
```
`گیٹ()` کسی بھی چیز کے ساتھ `فیوچر` لوٹاتا ہے یا `نل` اگر یہ موجود نہیں ہے۔ ایزار کے تمام آپریشنز بطور ڈیفالٹ غیر مطابقت پذیر ہوتے ہیں، اور ان میں سے اکثر کا ہم وقتی ہم منصب ہوتا ہے:

```dart
final recipe = isar.recipes.getSync(123);
```
:::warning
آپ کو اپنے یوآئی الگ تھلگ میں طریقوں کے غیر مطابقت پذیر ورژن پر ڈیفالٹ کرنا چاہئے۔ چونکہ ایزار بہت تیز ہے، یہ اکثر مطابقت پذیر ورژن استعمال کرنے کے لئے قابل قبول ہے.
:::

اگر آپ ایک ساتھ ایک سے زیادہ اشیاء حاصل کرنا چاہتے ہیں تو `گٹ آل ()` یا `گٹ آل سنک ()` استعمال کریں:

```dart
final recipe = await isar.recipes.getAll([1, 2]);
```

### اشیاء سے استفسار کریں۔

آی ڈی کے ذریعے آبجیکٹ حاصل کرنے کے بجائے آپ `.ویئر()` اور `.فیلٹر()` کا استعمال کرتے ہوئے مخصوص شرائط سے مماثل اشیاء کی فہرست سے بھی استفسار کر سکتے ہیں:

```dart
final allRecipes = await isar.recipes.where().findAll();

final favouires = await isar.recipes.filter()
  .isFavoriteEqualTo(true)
  .findAll();
```

➡️ مزید جانیں: [Queries](queries)

## ڈیٹا بیس میں ترمیم کرنا

آخر کار ہمارے کلیکشن میں ترمیم کرنے کا وقت آگیا ہے! اوبجیکٹس بنانے، اپ ڈیٹ کرنے یا حذف کرنے کے لیے، تحریری لین دین میں لپیٹے ہوئے متعلقہ آپریشنز کا استعمال کریں:

```dart
await isar.writeTxn(() async {
  final recipe = await isar.recipes.get(123)

  recipe.isFavorite = false;
  await isar.recipes.put(recipe); // perform update operations

  await isar.recipes.delete(123); // or delete operations
});
```

➡️ مزید جانیں:  [Transactions](transactions)

### آبجیکٹ داخل کریں۔

ایزار میں کسی چیز کو برقرار رکھنے کے لیے، اسے ایک کلیکشن میں داخل کریں۔ ایزار کا `پٹ()` طریقہ یا تو آبجیکٹ کو داخل یا اپ ڈیٹ کرے گا اس پر منحصر ہے کہ آیا یہ پہلے سے مجموعہ میں موجود ہے۔

اگر آئی ڈی فیلڈ `نل` یا `ایزار.آٹوانکریمنٹ` ہے تو ایزار ایک خودکار اضافہ آئی ڈی استعمال کرے گا۔

```dart
final pancakes = Recipe()
  ..name = 'Pancakes'
  ..lastCooked = DateTime.now()
  ..isFavorite = true;

await isar.writeTxn(() async {
  await isar.recipes.put(pancakes);
})
```

ایزار خود بخود آئی ڈی  آبجیکٹ کو تفویض کر دے گا اگر `آئی ڈی` فیلڈ غیر حتمی ہے۔

ایک ساتھ متعدد اشیاء کو داخل کرنا اتنا ہی آسان ہے:

```dart
await isar.writeTxn(() async {
  await isar.recipes.putAll([pancakes, pizza]);
})
```

### آبجیکٹ کو اپ ڈیٹ کریں۔

دونوں کو بنانا اور اپ ڈیٹ کرنا `کلیکشن.پت(ااوبجکٹ)` کے ساتھ کام کرتا ہے۔ اگر آئی ڈی نل ہے (یا موجود نہیں ہے) تو آبجیکٹ داخل کیا جاتا ہے۔ دوسری صورت میں، یہ اپ ڈیٹ کیا جاتا ہے.

لہذا اگر ہم اپنے پان کیکس کو ناپسند کرنا چاہتے ہیں، تو ہم درج ذیل کام کر سکتے ہیں:

```dart
await isar.writeTxn(() async {
  pancakes.isFavorite = false;
  await isar.recipes.put(recipe);
});
```
### آبجیکٹ کو حذف کریں۔

ایزار میں کسی چیز سے چھٹکارا حاصل کرنا چاہتے ہیں؟ `کلیکشن.ڈیلیٹ(آئی ڈی)` استعمال کریں۔ حذف کرنے کا طریقہ واپس کرتا ہے کہ آیا مخصوص آئی ڈی کے ساتھ کوئی آبجیکٹ ملا اور حذف کر دیا گیا تھا۔ اگر آپ آئی ڈی `123` کے ساتھ آبجیکٹ کو حذف کرنا چاہتے ہیں، مثال کے طور پر، آپ یہ کر سکتے ہیں:

```dart
await isar.writeTxn(() async {
  final success = await isar.recipes.delete(123);
  print('Recipe deleted: $success');
});
```

اسی طرح حاصل کرنے اور ڈالنے کے لئے، ایک بلک ڈیلیٹ آپریشن بھی ہے جو حذف شدہ اشیاء کی تعداد لوٹاتا ہے:

```dart
await isar.writeTxn(() async {
  final count = await isar.recipes.deleteAll([1, 2, 3]);
  print('We deleted $count recipes');
});
```

اگر آپ ان اشیاء کی آئی ڈی نہیں جانتے جنہیں آپ حذف کرنا چاہتے ہیں، تو آپ ایک استفسار استعمال کر سکتے ہیں:

```dart
await isar.writeTxn(() async {
  final count = await isar.recipes.filter()
    .isFavoriteEqualTo(false)
    .deleteAll();
  print('We deleted $count recipes');
});
```
