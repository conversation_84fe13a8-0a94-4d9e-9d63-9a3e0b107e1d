on: workflow_call

jobs:
  firebase_testlab_android:
    name: Firebase Testlab Android
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ vars.FLUTTER_VERSION }}
      - name: Prepare Build
        uses: ./.github/actions/prepare-build
      - name: Display Rust version
        run: rustc --version && cargo --version
      - name: Build Isar Core arm64
        run: |
          bash tool/build_android.sh arm64
          mkdir -p packages/isar_community_flutter_libs/android/src/main/jniLibs/arm64-v8a
          mv libisar_android_arm64.so packages/isar_community_flutter_libs/android/src/main/jniLibs/arm64-v8a/libisar.so
      - name: Build Isar Core armv7
        run: |
          bash tool/build_android.sh armv7
          mkdir -p packages/isar_community_flutter_libs/android/src/main/jniLibs/armeabi-v7a
          mv libisar_android_armv7.so packages/isar_community_flutter_libs/android/src/main/jniLibs/armeabi-v7a/libisar.so
      - name: Prepare Tests
        run: sh tool/prepare_tests.sh
      - name: Build dummy APK
        run: flutter build apk integration_test/integration_test.dart
        working-directory: packages/isar_test
      - name: Build APKs
        run: |
          ./gradlew app:assembleAndroidTest -Ptarget=integration_test/integration_test.dart
          ./gradlew app:assembleDebug -Ptarget=integration_test/integration_test.dart
        working-directory: packages/isar_test/android
      - name: Login to Google Cloud
        uses: "google-github-actions/auth@v1"
        with:
          credentials_json: "${{ secrets.FIREBASE_JSON }}"
      - name: Run tests
        run: |
          gcloud firebase test android run \
            --project isar-community \
            --type instrumentation \
            --timeout 5m \
            --device model=blueline,version=28 \
            --device model=t2q,version=34 \
            --device model=tokay,version=35 \
            --app build/app/outputs/apk/debug/app-debug.apk \
            --test build/app/outputs/apk/androidTest/debug/app-debug-androidTest.apk
        working-directory: packages/isar_test