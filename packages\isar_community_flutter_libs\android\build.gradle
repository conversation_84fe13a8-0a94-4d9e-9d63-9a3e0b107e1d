group 'dev.isar.isar_community_flutter_libs'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    namespace 'dev.isar.isar_community_flutter_libs'
    compileSdkVersion 35

    defaultConfig {
        minSdkVersion 23
    }
}

dependencies {
    implementation "androidx.startup:startup-runtime:1.1.1"
}