---
home: true
title: Home
heroImage: /isar.svg
actions:
  - text: Auf <PERSON> geht's los!
    link: /de/tutorials/quickstart.html
    type: primary
features:
  - title: 💙 Für Flutter gemacht
    details: Minimales Setup, ein<PERSON>ch zu bedienen, keine Konfiguration, kein Boilerplate. Mit ein paar Zeilen Code geht's los.
  - title: 🚀 Skalierbar
    details: Speichere Hunderttausende von Datensätzen und rufe sie effizient und asynchron ab.
  - title: 🍭 Viele Features
    details: Isar hat unzählige Features. Komposit- und Mehrfach-Indizes, Query-Modifikatoren, JSON und mehr.
  - title: 🔎 Volltextsuche
    details: Volltextsuche ist integriert. Erstelle einen Mehrfach-Index und suche nach Datensätzen.
  - title: 🧪 ACID Semantik
    details: Isar ist ACID-konform und verwaltet Transaktionen automatisch. Änderungen werden rückgängig gemacht, falls ein <PERSON>hler auftritt.
  - title: 💃 Statische Typisierung
    details: Abfragen sind statisch typisiert und werden zur Kompilierzeit überprüft. Laufzeitfehler sind ein Problem von gestern.
  - title: 📱 Multiplatform
    details: iOS, Android, Desktop und VOLLE WEB UNTERSTÜTZUNG!
  - title: ⏱ Asynchron
    details: Parallelle Abfragen und Multi-Isolate-Unterstützung.
  - title: 🦄 Open Source
    details: Komplett Open Source und für immer kostenlos!

footer: Apache Licensed | Copyright © 2022 Simon Leier
---
