<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Isar Inspector">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Isar Inspector">
  <link rel="apple-touch-icon" href="https://isar-community.dev/icon-256x256.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="https://isar-community.dev/icon-256x256.png" />

  <title>Isar Inspector</title>
  <link rel="manifest" href="manifest.json">
  <script defer src="flutter.js"></script>
  <style lang="css">
    body {
      background: #1a1c1e;
    }

    #loader {
      position: absolute;
      display: grid;
      grid-auto-flow: row;
      row-gap: 20px;
      justify-items: center;
      left: 50%;
      top: 50%;
      transform: translate(-50%, calc(-50% + 12pt));
      z-index: 5000;
    }

    #loader img {
      width: 120px;
      animation: cubic-bezier(0.74, 0.03, 0.37, 0.96) spin 1.5s infinite;
    }

    #loader #loader-info-text {
      font-family: sans-serif;
      font-size: 12pt;
      color: #fff;
      text-align: center;
    }

    #loader.hide  {
      animation: fade .5s;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      90% {
        transform: rotate(720deg);
      }
      100% {
        transform: rotate(720deg);
      }
    }

    @keyframes fade {
      from {
        opacity: 1;
      }
      to {
        opacity: 0;
      }
    }
  </style>
</head>

<body>
<section id="loader">
  <img src="assets/assets/logo.png" alt="Isar Logo">
  <span id="loader-info-text"></span>
</section>
  <!-- This script installs service_worker.js to provide PWA functionality to
       application. For more information, see:
       https://developers.google.com/web/fundamentals/primers/service-workers -->
  <script>
    var serviceWorkerVersion = null;

    window.addEventListener('load', function () {
      const loadingContainer = document.querySelector('#loader');
      const loading = document.querySelector('#loader-info-text');
      loading.textContent = "Loading entrypoint...";
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        }
      }).then(function(engineInitializer) {
        loading.textContent = "Initializing engine...";
        return engineInitializer.initializeEngine();
      }).then(function(appRunner) {
        loading.textContent = "Running app...";
        return appRunner.runApp();
      }).then(function (app) {
        // Wait a few milliseconds so users can see the "zoooom" animation
        // before getting rid of the "loading" div.
        loadingContainer.classList.add('hide');
        window.setTimeout(function () {
          loadingContainer.remove();
        }, 500);
      });
    });

  </script>
</body>

</html>
