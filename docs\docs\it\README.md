---
home: true
title: Home
heroImage: /isar.svg
actions:
  - text: Iniziamo!
    link: /it/tutorials/quickstart.html
    type: primary
features:
  - title: 💙 Creato per Flutter
    details: Setup minimo, facile da usare, nessuna configurazione, niente codice boilerplate. Basta aggiungere poche linee di codice per iniziare.
  - title: 🚀 Altamente scalabile
    details: Salva centinaia di migliaia di records in un singolo NoSQL database ed interrogalo in maniera efficiente ed asincrona.
  - title: 🍭 Ricco di funzionalità
    details: Isa ha un insieme ricco di funzionalità per aiutare nella gestione dei tuoi dati. Indici composti & multi-entry, modificatori di query, supporto al JSON, e molto altro. 
  - title: 🔎 Ricerca full-text
    details: Isar ha un sistema di ricerca built-in basato su full-text. Crea un inidice multi-entry e ricerca i record facilmente. 
  - title: 🧪 Semantica ACID 
    details: Isar è conforme con ACID e gestisce automaticamente le transazioni. In caso di errore effettua roll-back automaticamente. 
  - title: 💃 Staticamente tipizzato
    details: Le query Isar sono tipizzate staticamente e controllate in fase di compilazione. Non è necessario preoccuparsi degli errori di runtime.
  - title: 📱 Multipiattaform
    details: iOS, Android, Desktop e PIENO SUPPORTO AL WEB!
  - title: ⏱ Asincrono
    details: Operazioni di query parallele e supporto per isolamento multiplo pronto all'uso
  - title: 🦄 Open Source
    details: Tutto è open source e gratuito per sempre!

footer: Apache Licensed | Copyright © 2022 Simon Leier
---
