    // ignore_for_file: directives_ordering

    import 'package:isar_test/isar_test.dart';
    import '../test/clear_test.dart' as test_clear_test;
import '../test/collection_size_test.dart' as test_collection_size_test;
import '../test/compact_on_launch_test.dart' as test_compact_on_launch_test;
import '../test/constructor_test.dart' as test_constructor_test;
import '../test/copy_to_file_test.dart' as test_copy_to_file_test;
import '../test/crud_test.dart' as test_crud_test;
import '../test/default_value/default_test.dart' as test_default_value_default_test;
import '../test/default_value/no_default_test.dart' as test_default_value_no_default_test;
import '../test/default_value/nullable_test.dart' as test_default_value_nullable_test;
import '../test/embedded_test.dart' as test_embedded_test;
import '../test/enum_test.dart' as test_enum_test;
import '../test/filter/filter_bool_list_test.dart' as test_filter_filter_bool_list_test;
import '../test/filter/filter_bool_test.dart' as test_filter_filter_bool_test;
import '../test/filter/filter_byte_list_test.dart' as test_filter_filter_byte_list_test;
import '../test/filter/filter_byte_test.dart' as test_filter_filter_byte_test;
import '../test/filter/filter_date_time_list_test.dart' as test_filter_filter_date_time_list_test;
import '../test/filter/filter_date_time_test.dart' as test_filter_filter_date_time_test;
import '../test/filter/filter_embedded_list_test.dart' as test_filter_filter_embedded_list_test;
import '../test/filter/filter_embedded_test.dart' as test_filter_filter_embedded_test;
import '../test/filter/filter_float_list_test.dart' as test_filter_filter_float_list_test;
import '../test/filter/filter_float_test.dart' as test_filter_filter_float_test;
import '../test/filter/filter_id_test.dart' as test_filter_filter_id_test;
import '../test/filter/filter_int_test.dart' as test_filter_filter_int_test;
import '../test/filter/filter_list_length_test.dart' as test_filter_filter_list_length_test;
import '../test/filter/filter_string_list_test.dart' as test_filter_filter_string_list_test;
import '../test/filter/filter_string_test.dart' as test_filter_filter_string_test;
import '../test/filter/link/filter_backlinks_test.dart' as test_filter_link_filter_backlinks_test;
import '../test/filter/link/filter_backlink_test.dart' as test_filter_link_filter_backlink_test;
import '../test/filter/link/filter_links_self_test.dart' as test_filter_link_filter_links_self_test;
import '../test/filter/link/filter_links_test.dart' as test_filter_link_filter_links_test;
import '../test/filter/link/filter_link_circular_direct_test.dart' as test_filter_link_filter_link_circular_direct_test;
import '../test/filter/link/filter_link_circular_indirect_test.dart' as test_filter_link_filter_link_circular_indirect_test;
import '../test/filter/link/filter_link_nested_test.dart' as test_filter_link_filter_link_nested_test;
import '../test/filter/link/filter_link_self_test.dart' as test_filter_link_filter_link_self_test;
import '../test/filter/link/filter_link_test.dart' as test_filter_link_filter_link_test;
import '../test/id_test.dart' as test_id_test;
import '../test/index/composite2_test.dart' as test_index_composite2_test;
import '../test/index/composite3_test.dart' as test_index_composite3_test;
import '../test/index/composite_string_test.dart' as test_index_composite_string_test;
import '../test/index/get_by_delete_by_test.dart' as test_index_get_by_delete_by_test;
import '../test/index/multi_entry_test.dart' as test_index_multi_entry_test;
import '../test/index/put_by_test.dart' as test_index_put_by_test;
import '../test/index/where_bool_list_test.dart' as test_index_where_bool_list_test;
import '../test/index/where_bool_test.dart' as test_index_where_bool_test;
import '../test/index/where_byte_list_test.dart' as test_index_where_byte_list_test;
import '../test/index/where_byte_test.dart' as test_index_where_byte_test;
import '../test/index/where_date_time_list_test.dart' as test_index_where_date_time_list_test;
import '../test/index/where_date_time_test.dart' as test_index_where_date_time_test;
import '../test/index/where_float_list_test.dart' as test_index_where_float_list_test;
import '../test/index/where_float_test.dart' as test_index_where_float_test;
import '../test/index/where_id_test.dart' as test_index_where_id_test;
import '../test/index/where_int_test.dart' as test_index_where_int_test;
import '../test/index/where_string_list_test.dart' as test_index_where_string_list_test;
import '../test/index/where_string_test.dart' as test_index_where_string_test;
import '../test/inheritance_test.dart' as test_inheritance_test;
import '../test/instance_test.dart' as test_instance_test;
import '../test/isolate_test.dart' as test_isolate_test;
import '../test/json_test.dart' as test_json_test;
import '../test/links/backlink_test.dart' as test_links_backlink_test;
import '../test/links/links_test.dart' as test_links_links_test;
import '../test/links/link_test.dart' as test_links_link_test;
import '../test/link_test.dart' as test_link_test;
import '../test/max_size_test.dart' as test_max_size_test;
import '../test/migration/add_remove_collection_test.dart' as test_migration_add_remove_collection_test;
import '../test/migration/add_remove_embedded_field_test.dart' as test_migration_add_remove_embedded_field_test;
import '../test/migration/add_remove_field_test.dart' as test_migration_add_remove_field_test;
import '../test/migration/add_remove_index_test.dart' as test_migration_add_remove_index_test;
import '../test/migration/add_remove_link_test.dart' as test_migration_add_remove_link_test;
import '../test/migration/change_field_embedded_test.dart' as test_migration_change_field_embedded_test;
import '../test/migration/change_field_nullability_test.dart' as test_migration_change_field_nullability_test;
import '../test/migration/change_field_type_test.dart' as test_migration_change_field_type_test;
import '../test/migration/change_link_links_test.dart' as test_migration_change_link_links_test;
import '../test/name_test.dart' as test_name_test;
import '../test/open_close_isar_listener_test.dart' as test_open_close_isar_listener_test;
import '../test/other_test.dart' as test_other_test;
import '../test/query/aggregation_test.dart' as test_query_aggregation_test;
import '../test/query/embedded_test.dart' as test_query_embedded_test;
import '../test/query/group_test.dart' as test_query_group_test;
import '../test/query/is_empty_is_not_empty_test.dart' as test_query_is_empty_is_not_empty_test;
import '../test/query/multi_filter_test.dart' as test_query_multi_filter_test;
import '../test/query/offset_limit_test.dart' as test_query_offset_limit_test;
import '../test/query/property_test.dart' as test_query_property_test;
import '../test/query/sort_by_distinct_by_test.dart' as test_query_sort_by_distinct_by_test;
import '../test/query/where_sort_distinct_test.dart' as test_query_where_sort_distinct_test;
import '../test/regression/issue_235_rename_field_test.dart' as test_regression_issue_235_rename_field_test;
import '../test/schema_test.dart' as test_schema_test;
import '../test/stress/long_string_test.dart' as test_stress_long_string_test;
import '../test/stress/twitter_test.dart' as test_stress_twitter_test;
import '../test/transaction_test.dart' as test_transaction_test;
import '../test/watcher_test.dart' as test_watcher_test;
import '../test/widget_test.dart' as test_widget_test;

    void main() {
      const stress = bool.fromEnvironment('STRESS');
      test_clear_test.main();
test_collection_size_test.main();
test_compact_on_launch_test.main();
test_constructor_test.main();
test_copy_to_file_test.main();
test_crud_test.main();
test_default_value_default_test.main();
test_default_value_no_default_test.main();
test_default_value_nullable_test.main();
test_embedded_test.main();
test_enum_test.main();
test_filter_filter_bool_list_test.main();
test_filter_filter_bool_test.main();
test_filter_filter_byte_list_test.main();
test_filter_filter_byte_test.main();
test_filter_filter_date_time_list_test.main();
test_filter_filter_date_time_test.main();
test_filter_filter_embedded_list_test.main();
test_filter_filter_embedded_test.main();
test_filter_filter_float_list_test.main();
test_filter_filter_float_test.main();
test_filter_filter_id_test.main();
test_filter_filter_int_test.main();
test_filter_filter_list_length_test.main();
test_filter_filter_string_list_test.main();
test_filter_filter_string_test.main();
test_filter_link_filter_backlinks_test.main();
test_filter_link_filter_backlink_test.main();
test_filter_link_filter_links_self_test.main();
test_filter_link_filter_links_test.main();
test_filter_link_filter_link_circular_direct_test.main();
test_filter_link_filter_link_circular_indirect_test.main();
test_filter_link_filter_link_nested_test.main();
test_filter_link_filter_link_self_test.main();
test_filter_link_filter_link_test.main();
test_id_test.main();
test_index_composite2_test.main();
test_index_composite3_test.main();
test_index_composite_string_test.main();
test_index_get_by_delete_by_test.main();
test_index_multi_entry_test.main();
test_index_put_by_test.main();
test_index_where_bool_list_test.main();
test_index_where_bool_test.main();
test_index_where_byte_list_test.main();
test_index_where_byte_test.main();
test_index_where_date_time_list_test.main();
test_index_where_date_time_test.main();
test_index_where_float_list_test.main();
test_index_where_float_test.main();
test_index_where_id_test.main();
test_index_where_int_test.main();
test_index_where_string_list_test.main();
test_index_where_string_test.main();
test_inheritance_test.main();
test_instance_test.main();
if (!kIsWeb) test_isolate_test.main();
test_json_test.main();
test_links_backlink_test.main();
test_links_links_test.main();
test_links_link_test.main();
test_link_test.main();
test_max_size_test.main();
test_migration_add_remove_collection_test.main();
test_migration_add_remove_embedded_field_test.main();
test_migration_add_remove_field_test.main();
test_migration_add_remove_index_test.main();
test_migration_add_remove_link_test.main();
test_migration_change_field_embedded_test.main();
test_migration_change_field_nullability_test.main();
if (!kIsWeb) test_migration_change_field_type_test.main();
test_migration_change_link_links_test.main();
test_name_test.main();
test_open_close_isar_listener_test.main();
test_other_test.main();
test_query_aggregation_test.main();
test_query_embedded_test.main();
test_query_group_test.main();
test_query_is_empty_is_not_empty_test.main();
test_query_multi_filter_test.main();
test_query_offset_limit_test.main();
test_query_property_test.main();
test_query_sort_by_distinct_by_test.main();
test_query_where_sort_distinct_test.main();
test_regression_issue_235_rename_field_test.main();
test_schema_test.main();
if (stress) test_stress_long_string_test.main();
if (stress) test_stress_twitter_test.main();
test_transaction_test.main();
test_watcher_test.main();
test_widget_test.main();
    }
