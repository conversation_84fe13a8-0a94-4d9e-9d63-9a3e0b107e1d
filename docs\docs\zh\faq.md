---
title: 常见疑问
---

# 常见疑问

关于 Isar 和 Flutter 数据库的常见问题集合。

### 为什么我需要一个数据库？

> 我把数据存在后台服务器的数据库，为什么还需要 Isar？

即便在今天，你可能也会遇到没有网络连接的时候，比如在地铁里、飞机上或者拜访老一辈的时候（你懂的）。网络不好也就意味着无法连接到后台数据库，所以你应该用离线数据库来增强你 App 的使用体验！

### Isar 对比 Hive

答案很简单：Isar [就是为了代替 Hive 而生的](https://github.com/hivedb/hive/issues/246)，现阶段我都会毫不犹豫地推荐 Isar 而不是 Hive。

### Where 子句？！

> 为什么 **_我_** 必须选择使用何种索引？

有多方面的原因。许多数据库会针对给定查询启发式选择最佳索引。数据库需要收集额外的使用信息（-> 额外的性能开销），而且可能仍然会选到错误的索引，从而导致查询变得更慢。

没有人比你，作为开发者，更了解你的数据。所以你大可根据实际情况选择最优索引，甚至可以决定是否需要用索引来做查询或排序。

### 我是否必须用索引或 where 子句?

不必！如果你只用 Filter，Isar 也已经足够快了。

### Isar 真的足够快？

在针对移动端的数据库中，Isar 在性能方面名列前茅，所以对于大多数应用场景，它本身已经足够快了。如果你遇到了性能问题，很大可能是哪里做得不对。

### Isar 会增加我 App 的打包大小吗？

是的，但是很小。 Isar 会给你的 App 增加 1 - 1.5 MB 的下载大小，Web 端则仅增加几 KB。

### 文档有疏漏或错误。

好吧，对不起。 请[在此开一个 Issue](https://github.com/isar-community/isar/issues/new/choose) 或者更好的话，提交一个 PR 来帮助修复 💪。
